/**
 * 本地存储工具类
 * 提供localStorage和sessionStorage的封装，支持JSON数据和过期时间
 */
window.StorageUtils = {

  /**
   * 设置localStorage数据
   * @param {string} key - 存储键名
   * @param {*} value - 存储值
   * @param {number} expireTime - 过期时间（毫秒），可选
   * @returns {boolean} 是否存储成功
   */
  setLocal(key, value, expireTime = null) {
    try {
      const data = {
        value: value,
        timestamp: Date.now(),
        expire: expireTime ? Date.now() + expireTime : null
      };
      
      localStorage.setItem(key, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('localStorage存储失败:', error);
      return false;
    }
  },

  /**
   * 获取localStorage数据
   * @param {string} key - 存储键名
   * @param {*} defaultValue - 默认值
   * @returns {*} 存储的值或默认值
   */
  getLocal(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(key);
      if (!item) {
        return defaultValue;
      }

      const data = JSON.parse(item);
      
      // 检查是否过期
      if (data.expire && Date.now() > data.expire) {
        this.removeLocal(key);
        return defaultValue;
      }

      return data.value;
    } catch (error) {
      console.error('localStorage读取失败:', error);
      return defaultValue;
    }
  },

  /**
   * 移除localStorage数据
   * @param {string} key - 存储键名
   * @returns {boolean} 是否移除成功
   */
  removeLocal(key) {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('localStorage移除失败:', error);
      return false;
    }
  },

  /**
   * 清空localStorage
   * @returns {boolean} 是否清空成功
   */
  clearLocal() {
    try {
      localStorage.clear();
      return true;
    } catch (error) {
      console.error('localStorage清空失败:', error);
      return false;
    }
  },

  /**
   * 检查localStorage中是否存在指定键
   * @param {string} key - 存储键名
   * @returns {boolean} 是否存在
   */
  hasLocal(key) {
    return localStorage.getItem(key) !== null;
  },

  /**
   * 获取localStorage中所有键名
   * @returns {Array<string>} 键名数组
   */
  getLocalKeys() {
    const keys = [];
    for (let i = 0; i < localStorage.length; i++) {
      keys.push(localStorage.key(i));
    }
    return keys;
  },

  /**
   * 设置sessionStorage数据
   * @param {string} key - 存储键名
   * @param {*} value - 存储值
   * @returns {boolean} 是否存储成功
   */
  setSession(key, value) {
    try {
      const data = {
        value: value,
        timestamp: Date.now()
      };
      
      sessionStorage.setItem(key, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('sessionStorage存储失败:', error);
      return false;
    }
  },

  /**
   * 获取sessionStorage数据
   * @param {string} key - 存储键名
   * @param {*} defaultValue - 默认值
   * @returns {*} 存储的值或默认值
   */
  getSession(key, defaultValue = null) {
    try {
      const item = sessionStorage.getItem(key);
      if (!item) {
        return defaultValue;
      }

      const data = JSON.parse(item);
      return data.value;
    } catch (error) {
      console.error('sessionStorage读取失败:', error);
      return defaultValue;
    }
  },

  /**
   * 移除sessionStorage数据
   * @param {string} key - 存储键名
   * @returns {boolean} 是否移除成功
   */
  removeSession(key) {
    try {
      sessionStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('sessionStorage移除失败:', error);
      return false;
    }
  },

  /**
   * 清空sessionStorage
   * @returns {boolean} 是否清空成功
   */
  clearSession() {
    try {
      sessionStorage.clear();
      return true;
    } catch (error) {
      console.error('sessionStorage清空失败:', error);
      return false;
    }
  },

  /**
   * 检查sessionStorage中是否存在指定键
   * @param {string} key - 存储键名
   * @returns {boolean} 是否存在
   */
  hasSession(key) {
    return sessionStorage.getItem(key) !== null;
  },

  /**
   * 获取存储空间使用情况
   * @param {string} storageType - 存储类型 'local' 或 'session'
   * @returns {Object} 使用情况对象
   */
  getStorageInfo(storageType = 'local') {
    const storage = storageType === 'local' ? localStorage : sessionStorage;
    let totalSize = 0;
    let itemCount = 0;

    try {
      for (let key in storage) {
        if (storage.hasOwnProperty(key)) {
          totalSize += storage[key].length + key.length;
          itemCount++;
        }
      }

      return {
        itemCount,
        totalSize,
        totalSizeKB: Math.round(totalSize / 1024 * 100) / 100,
        available: this.isStorageAvailable(storageType)
      };
    } catch (error) {
      console.error('获取存储信息失败:', error);
      return {
        itemCount: 0,
        totalSize: 0,
        totalSizeKB: 0,
        available: false
      };
    }
  },

  /**
   * 检查存储是否可用
   * @param {string} storageType - 存储类型 'local' 或 'session'
   * @returns {boolean} 是否可用
   */
  isStorageAvailable(storageType = 'local') {
    try {
      const storage = storageType === 'local' ? localStorage : sessionStorage;
      const testKey = '__storage_test__';
      storage.setItem(testKey, 'test');
      storage.removeItem(testKey);
      return true;
    } catch (error) {
      return false;
    }
  },

  /**
   * 批量设置数据
   * @param {Object} data - 数据对象
   * @param {string} storageType - 存储类型 'local' 或 'session'
   * @returns {boolean} 是否全部设置成功
   */
  setBatch(data, storageType = 'local') {
    let allSuccess = true;
    
    Object.entries(data).forEach(([key, value]) => {
      const success = storageType === 'local' 
        ? this.setLocal(key, value)
        : this.setSession(key, value);
      
      if (!success) {
        allSuccess = false;
      }
    });

    return allSuccess;
  },

  /**
   * 批量获取数据
   * @param {Array<string>} keys - 键名数组
   * @param {string} storageType - 存储类型 'local' 或 'session'
   * @returns {Object} 数据对象
   */
  getBatch(keys, storageType = 'local') {
    const result = {};
    
    keys.forEach(key => {
      result[key] = storageType === 'local'
        ? this.getLocal(key)
        : this.getSession(key);
    });

    return result;
  },

  /**
   * 批量移除数据
   * @param {Array<string>} keys - 键名数组
   * @param {string} storageType - 存储类型 'local' 或 'session'
   * @returns {boolean} 是否全部移除成功
   */
  removeBatch(keys, storageType = 'local') {
    let allSuccess = true;
    
    keys.forEach(key => {
      const success = storageType === 'local'
        ? this.removeLocal(key)
        : this.removeSession(key);
      
      if (!success) {
        allSuccess = false;
      }
    });

    return allSuccess;
  },

  /**
   * 清理过期的localStorage数据
   * @returns {number} 清理的项目数量
   */
  cleanExpired() {
    let cleanedCount = 0;
    const keys = this.getLocalKeys();

    keys.forEach(key => {
      try {
        const item = localStorage.getItem(key);
        if (item) {
          const data = JSON.parse(item);
          if (data.expire && Date.now() > data.expire) {
            this.removeLocal(key);
            cleanedCount++;
          }
        }
      } catch (error) {
        // 如果解析失败，可能是非本工具存储的数据，跳过
      }
    });

    return cleanedCount;
  },

  /**
   * 导出存储数据
   * @param {string} storageType - 存储类型 'local' 或 'session'
   * @returns {Object} 导出的数据对象
   */
  exportData(storageType = 'local') {
    const storage = storageType === 'local' ? localStorage : sessionStorage;
    const exportData = {};

    try {
      for (let i = 0; i < storage.length; i++) {
        const key = storage.key(i);
        exportData[key] = storage.getItem(key);
      }
      return exportData;
    } catch (error) {
      console.error('导出数据失败:', error);
      return {};
    }
  },

  /**
   * 导入存储数据
   * @param {Object} data - 要导入的数据对象
   * @param {string} storageType - 存储类型 'local' 或 'session'
   * @param {boolean} clearFirst - 是否先清空现有数据
   * @returns {boolean} 是否导入成功
   */
  importData(data, storageType = 'local', clearFirst = false) {
    try {
      const storage = storageType === 'local' ? localStorage : sessionStorage;
      
      if (clearFirst) {
        storage.clear();
      }

      Object.entries(data).forEach(([key, value]) => {
        storage.setItem(key, value);
      });

      return true;
    } catch (error) {
      console.error('导入数据失败:', error);
      return false;
    }
  }
};
