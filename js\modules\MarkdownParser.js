/**
 * Markdown解析模块
 * 负责解析Markdown内容并生成树形结构数据
 */
class MarkdownParser {
  constructor(eventManager) {
    this.eventManager = eventManager;
    this.roadmapData = [];
    this.itemIdCounter = 0;

    this.init();
  }

  /**
   * 初始化解析模块
   */
  init() {
    this.bindEvents();
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 监听文件解析事件
    this.eventManager.on(AppConstants.EVENTS.FILE_PARSED, (data) => {
      this.parseMarkdownContent(data.content);
    });

    // 监听数据清空事件
    this.eventManager.on(AppConstants.EVENTS.DATA_CLEARED, () => {
      this.clearData();
    });
  }

  /**
   * 解析Markdown内容
   * @param {string} markdownContent - Markdown文本内容
   */
  parseMarkdownContent(markdownContent) {
    if (!markdownContent || typeof markdownContent !== 'string') {
      console.error('无效的Markdown内容');
      return;
    }

    try {
      const lines = markdownContent.split('\n');
      this.roadmapData = [];
      this.itemIdCounter = 0;

      // 使用栈结构管理当前嵌套层级
      const levelStack = [];

      lines.forEach(line => {
        const trimmedLine = line.trim();
        if (!trimmedLine) return;

        // 标题解析（处理所有级别标题）
        const headerMatch = trimmedLine.match(/^(#{1,6})\s+(.+)$/);
        if (headerMatch) {
          this.processHeader(headerMatch, levelStack);
        }
        // 列表项解析
        else if (trimmedLine.startsWith('-') || trimmedLine.startsWith('*')) {
          this.processListItem(trimmedLine, levelStack);
        }
      });

      // 触发路线图更新事件
      this.eventManager.emit(AppConstants.EVENTS.ROADMAP_UPDATED, {
        roadmapData: this.roadmapData
      });

      console.log('Markdown解析完成，生成项目数:', this.countAllItems());

    } catch (error) {
      console.error('Markdown解析失败:', error);
      this.eventManager.emit(AppConstants.EVENTS.FILE_UPLOAD_ERROR, {
        message: AppConstants.ERROR_MESSAGES.PARSE_ERROR
      });
    }
  }

  /**
   * 处理标题行
   * @param {Array} headerMatch - 正则匹配结果
   * @param {Array} levelStack - 层级栈
   */
  processHeader(headerMatch, levelStack) {
    const originalLevel = headerMatch[1].length;
    const title = headerMatch[2];

    // 创建新节点
    const newNode = this.createNode({
      type: this.getNodeType(originalLevel),
      level: originalLevel,
      title,
      showInSidebar: originalLevel <= AppConstants.CONFIG.SIDEBAR_MAX_LEVEL
    });

    // 处理层级关系
    this.processNodeHierarchy(newNode, levelStack);
  }

  /**
   * 处理列表项
   * @param {string} trimmedLine - 处理后的行内容
   * @param {Array} levelStack - 层级栈
   */
  processListItem(trimmedLine, levelStack) {
    const title = trimmedLine.replace(/^[-*]\s*/, '');
    const newItem = this.createNode({
      type: AppConstants.CONFIG.ITEM_TYPES.ITEM,
      level: levelStack.length > 0 ? levelStack[levelStack.length - 1].level + 1 : 1,
      title,
      timeSpent: 0,
      notes: ''
    });

    if (levelStack.length > 0) {
      levelStack[levelStack.length - 1].children.push(newItem);
    } else {
      this.roadmapData.push(newItem);
    }
  }

  /**
   * 创建节点对象
   * @param {Object} options - 节点选项
   * @returns {Object} 节点对象
   */
  createNode(options) {
    const node = {
      id: ++this.itemIdCounter,
      type: options.type || AppConstants.CONFIG.ITEM_TYPES.ITEM,
      level: options.level || 1,
      title: options.title || '',
      completed: false,
      collapsed: false,
      children: [],
      showInSidebar: options.showInSidebar !== undefined ? options.showInSidebar : true,
      ...options
    };

    // 如果是学习项目类型，添加额外属性
    if (node.type === AppConstants.CONFIG.ITEM_TYPES.ITEM) {
      node.timeSpent = options.timeSpent || 0;
      node.notes = options.notes || '';
    }

    return node;
  }

  /**
   * 根据标题级别确定节点类型
   * @param {number} level - 标题级别
   * @returns {string} 节点类型
   */
  getNodeType(level) {
    switch (level) {
      case 1:
        return AppConstants.CONFIG.ITEM_TYPES.SECTION;
      case 2:
        return AppConstants.CONFIG.ITEM_TYPES.SUBSECTION;
      default:
        return AppConstants.CONFIG.ITEM_TYPES.ITEM;
    }
  }

  /**
   * 处理节点层级关系
   * @param {Object} newNode - 新节点
   * @param {Array} levelStack - 层级栈
   */
  processNodeHierarchy(newNode, levelStack) {
    // 处理层级关系：移除比当前级别高或相等的节点
    while (levelStack.length > 0 && levelStack[levelStack.length - 1].level >= newNode.level) {
      levelStack.pop();
    }

    // 添加到适当的父节点
    if (levelStack.length === 0) {
      this.roadmapData.push(newNode);
    } else {
      levelStack[levelStack.length - 1].children.push(newNode);
    }

    // 将当前节点加入栈中
    levelStack.push(newNode);
  }

  /**
   * 统计所有项目数量
   * @param {Array} items - 项目数组，默认为根数据
   * @returns {number} 项目总数
   */
  countAllItems(items = this.roadmapData) {
    let count = 0;
    
    items.forEach(item => {
      if (item.type === AppConstants.CONFIG.ITEM_TYPES.ITEM) {
        count += 1;
      }
      if (item.children && item.children.length > 0) {
        count += this.countAllItems(item.children);
      }
    });

    return count;
  }

  /**
   * 统计已完成项目数量
   * @param {Array} items - 项目数组，默认为根数据
   * @returns {number} 已完成项目数
   */
  countCompletedItems(items = this.roadmapData) {
    let count = 0;
    
    items.forEach(item => {
      if (item.type === AppConstants.CONFIG.ITEM_TYPES.ITEM && item.completed) {
        count += 1;
      }
      if (item.children && item.children.length > 0) {
        count += this.countCompletedItems(item.children);
      }
    });

    return count;
  }

  /**
   * 根据ID查找项目
   * @param {number} id - 项目ID
   * @param {Array} items - 搜索范围，默认为根数据
   * @returns {Object|null} 找到的项目或null
   */
  findItemById(id, items = this.roadmapData) {
    for (let item of items) {
      if (item.id === id) {
        return item;
      }
      if (item.children && item.children.length > 0) {
        const found = this.findItemById(id, item.children);
        if (found) {
          return found;
        }
      }
    }
    return null;
  }

  /**
   * 获取路线图数据
   * @returns {Array} 路线图数据
   */
  getRoadmapData() {
    return this.roadmapData;
  }

  /**
   * 设置路线图数据
   * @param {Array} data - 路线图数据
   */
  setRoadmapData(data) {
    if (Array.isArray(data)) {
      this.roadmapData = data;
      
      // 更新ID计数器，确保新生成的ID不重复
      this.updateItemIdCounter();
      
      // 触发更新事件
      this.eventManager.emit(AppConstants.EVENTS.ROADMAP_UPDATED, {
        roadmapData: this.roadmapData
      });
    }
  }

  /**
   * 更新项目ID计数器
   */
  updateItemIdCounter() {
    let maxId = 0;
    
    const findMaxId = (items) => {
      items.forEach(item => {
        if (item.id > maxId) {
          maxId = item.id;
        }
        if (item.children && item.children.length > 0) {
          findMaxId(item.children);
        }
      });
    };

    findMaxId(this.roadmapData);
    this.itemIdCounter = maxId;
  }

  /**
   * 清空数据
   */
  clearData() {
    this.roadmapData = [];
    this.itemIdCounter = 0;
  }

  /**
   * 验证路线图数据结构
   * @param {Array} data - 要验证的数据
   * @returns {boolean} 是否有效
   */
  validateRoadmapData(data) {
    if (!Array.isArray(data)) {
      return false;
    }

    const validateItem = (item) => {
      // 检查必需属性
      if (!item.id || !item.title || !item.type || !item.level) {
        return false;
      }

      // 检查类型是否有效
      const validTypes = Object.values(AppConstants.CONFIG.ITEM_TYPES);
      if (!validTypes.includes(item.type)) {
        return false;
      }

      // 递归验证子项目
      if (item.children && Array.isArray(item.children)) {
        return item.children.every(validateItem);
      }

      return true;
    };

    return data.every(validateItem);
  }

  /**
   * 获取解析统计信息
   * @returns {Object} 统计信息
   */
  getParseStats() {
    return {
      totalItems: this.countAllItems(),
      completedItems: this.countCompletedItems(),
      sections: this.roadmapData.filter(item => 
        item.type === AppConstants.CONFIG.ITEM_TYPES.SECTION
      ).length,
      subsections: this.countItemsByType(AppConstants.CONFIG.ITEM_TYPES.SUBSECTION),
      items: this.countItemsByType(AppConstants.CONFIG.ITEM_TYPES.ITEM)
    };
  }

  /**
   * 按类型统计项目数量
   * @param {string} type - 项目类型
   * @param {Array} items - 搜索范围
   * @returns {number} 指定类型的项目数量
   */
  countItemsByType(type, items = this.roadmapData) {
    let count = 0;
    
    items.forEach(item => {
      if (item.type === type) {
        count += 1;
      }
      if (item.children && item.children.length > 0) {
        count += this.countItemsByType(type, item.children);
      }
    });

    return count;
  }

  /**
   * 销毁模块
   */
  destroy() {
    // 移除事件监听器
    this.eventManager.removeAllListeners(AppConstants.EVENTS.FILE_PARSED);
    this.eventManager.removeAllListeners(AppConstants.EVENTS.DATA_CLEARED);
    
    // 清理数据
    this.clearData();
  }
}

// 导出模块
window.MarkdownParser = MarkdownParser;
