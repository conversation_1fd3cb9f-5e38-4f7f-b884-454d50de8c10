/**
 * 渲染引擎模块
 * 负责DOM渲染、事件绑定、视图更新
 */
class RenderEngine {
  constructor(eventManager) {
    this.eventManager = eventManager;
    this.roadmapData = [];
    this.selectedItem = null;
    this.searchTerm = '';

    // DOM元素引用
    this.elements = {
      roadmapContent: DOMUtils.$(AppConstants.SELECTORS.ROADMAP_CONTENT),
      contentTitle: DOMUtils.$(AppConstants.SELECTORS.CONTENT_TITLE),
      contentBody: DOMUtils.$(AppConstants.SELECTORS.CONTENT_BODY),
      markCompleteBtn: DOMUtils.$(AppConstants.SELECTORS.MARK_COMPLETE_BTN),
      startStudyBtn: DOMUtils.$(AppConstants.SELECTORS.START_STUDY_BTN)
    };

    this.init();
  }

  /**
   * 初始化渲染引擎
   */
  init() {
    this.bindEvents();
    this.showEmptyState();
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 监听路线图更新事件
    this.eventManager.on(AppConstants.EVENTS.ROADMAP_UPDATED, (data) => {
      this.roadmapData = data.roadmapData;
      this.renderRoadmap();
    });

    // 监听数据加载事件
    this.eventManager.on(AppConstants.EVENTS.DATA_LOADED, (data) => {
      this.roadmapData = data.roadmapData;
      this.renderRoadmap();
    });

    // 监听数据清空事件
    this.eventManager.on(AppConstants.EVENTS.DATA_CLEARED, () => {
      this.roadmapData = [];
      this.selectedItem = null;
      this.showEmptyState();
      this.resetContentArea();
    });

    // 监听搜索事件
    this.eventManager.on(AppConstants.EVENTS.SEARCH_PERFORMED, (data) => {
      this.searchTerm = data.searchTerm;
      this.renderRoadmap();
    });

    // 监听搜索清空事件
    this.eventManager.on(AppConstants.EVENTS.SEARCH_CLEARED, () => {
      this.searchTerm = '';
      this.renderRoadmap();
    });

    // 监听项目选择事件
    this.eventManager.on(AppConstants.EVENTS.ITEM_SELECTED, (data) => {
      this.showItemContent(data.item);
    });

    // 监听统计更新事件（用于刷新显示）
    this.eventManager.on(AppConstants.EVENTS.STATS_UPDATED, () => {
      this.renderRoadmap();
    });

    // 绑定内容区域按钮事件
    this.bindContentAreaEvents();
  }

  /**
   * 绑定内容区域按钮事件
   */
  bindContentAreaEvents() {
    if (this.elements.markCompleteBtn) {
      DOMUtils.on(this.elements.markCompleteBtn, 'click', () => {
        if (this.selectedItem) {
          this.eventManager.emit(AppConstants.EVENTS.ITEM_COMPLETED, {
            id: this.selectedItem.id,
            completed: !this.selectedItem.completed
          });
          // 刷新内容显示
          this.showItemContent(this.selectedItem);
        }
      });
    }

    if (this.elements.startStudyBtn) {
      DOMUtils.on(this.elements.startStudyBtn, 'click', () => {
        if (this.selectedItem) {
          this.eventManager.emit(AppConstants.EVENTS.STUDY_SESSION_STARTED, {
            item: this.selectedItem
          });
        }
      });
    }
  }

  /**
   * 渲染学习路线
   */
  renderRoadmap() {
    if (!this.elements.roadmapContent) return;

    if (this.roadmapData.length === 0) {
      this.showEmptyState();
      return;
    }

    // 应用搜索过滤
    const filteredData = this.filterItemsBySearch(this.roadmapData);

    if (filteredData.length === 0 && this.searchTerm) {
      this.showNoSearchResults();
      return;
    }

    // 只渲染侧边栏显示的项目（前2级）
    const sidebarData = this.filterSidebarItems(filteredData);
    const html = sidebarData.map(item => this.renderItem(item)).join('');

    DOMUtils.setContent(this.elements.roadmapContent, html, true);
    this.bindItemEvents();
  }

  /**
   * 渲染单个项目
   * @param {Object} item - 项目对象
   * @param {number} depth - 嵌套深度
   * @returns {string} HTML字符串
   */
  renderItem(item, depth = 0) {
    const indent = depth * 20;
    const hasChildren = item.children?.length > 0;

    // 基础样式类
    const baseClass = item.type === AppConstants.CONFIG.ITEM_TYPES.SECTION ? 'tree-section' :
                      item.type === AppConstants.CONFIG.ITEM_TYPES.SUBSECTION ? 'tree-subsection' : 'tree-item';

    // 生成子元素HTML
    const childrenHtml = hasChildren ? `
      <div class="tree-children ${item.collapsed ? 'collapsed' : ''}">
        ${item.children.map(child => this.renderItem(child, depth + 1)).join('')}
      </div>
    ` : '';

    // 生成当前节点HTML
    if (item.type === AppConstants.CONFIG.ITEM_TYPES.SECTION ||
        item.type === AppConstants.CONFIG.ITEM_TYPES.SUBSECTION) {
      return this.renderSectionItem(item, baseClass, indent, hasChildren, childrenHtml);
    } else {
      return this.renderLeafItem(item, baseClass, indent);
    }
  }

  /**
   * 渲染章节/小节项目
   * @param {Object} item - 项目对象
   * @param {string} baseClass - 基础CSS类
   * @param {number} indent - 缩进像素
   * @param {boolean} hasChildren - 是否有子项目
   * @param {string} childrenHtml - 子项目HTML
   * @returns {string} HTML字符串
   */
  renderSectionItem(item, baseClass, indent, hasChildren, childrenHtml) {
    return `
      <div class="${baseClass}" style="margin-left: ${indent}px">
        <div class="tree-node-header ${item.completed ? 'completed' : ''}" data-id="${item.id}">
          <button class="collapse-btn ${item.collapsed ? 'collapsed' : ''}" data-id="${item.id}">
            <span class="collapse-icon">${hasChildren ? '▶' : ''}</span>
          </button>
          <h${item.level + 2} class="node-title">${item.title}</h${item.level + 2}>
          <span class="progress-indicator">${this.getChildrenProgress(item)}</span>
          <button class="btn-checkbox ${item.completed ? 'checked' : ''}" data-id="${item.id}" title="标记完成">
            <span class="checkmark">✓</span>
          </button>
        </div>
        ${childrenHtml}
      </div>
    `;
  }

  /**
   * 渲染叶子项目
   * @param {Object} item - 项目对象
   * @param {string} baseClass - 基础CSS类
   * @param {number} indent - 缩进像素
   * @returns {string} HTML字符串
   */
  renderLeafItem(item, baseClass, indent) {
    return `
      <div class="${baseClass} ${item.completed ? 'completed' : ''}" style="margin-left: ${indent}px" data-id="${item.id}">
        <div class="tree-node-content">
          <button class="btn-checkbox ${item.completed ? 'checked' : ''}" data-id="${item.id}" title="标记完成">
            <span class="checkmark">✓</span>
          </button>
          <span class="item-title">${item.title}</span>
          <span class="time-spent">${TimeUtils.formatTime(item.timeSpent || 0)}</span>
          <button class="btn-timer" data-id="${item.id}" title="开始计时">⏱️</button>
        </div>
        ${item.notes ? `<div class="item-notes">${item.notes}</div>` : ''}
      </div>
    `;
  }

  /**
   * 获取子项目进度
   * @param {Object} item - 项目对象
   * @returns {string} 进度字符串
   */
  getChildrenProgress(item) {
    if (!item.children || item.children.length === 0) return '';

    const total = this.countAllItems(item);
    const completed = this.countCompletedItems(item);
    return `${completed}/${total}`;
  }

  /**
   * 统计所有项目数量
   * @param {Object} item - 项目对象
   * @returns {number} 项目总数
   */
  countAllItems(item) {
    let count = 0;
    if (item.type === AppConstants.CONFIG.ITEM_TYPES.ITEM) count = 1;

    if (item.children) {
      item.children.forEach(child => {
        count += this.countAllItems(child);
      });
    }

    return count;
  }

  /**
   * 统计已完成项目数量
   * @param {Object} item - 项目对象
   * @returns {number} 已完成项目数
   */
  countCompletedItems(item) {
    let count = 0;
    if (item.type === AppConstants.CONFIG.ITEM_TYPES.ITEM && item.completed) count = 1;

    if (item.children) {
      item.children.forEach(child => {
        count += this.countCompletedItems(child);
      });
    }

    return count;
  }

  /**
   * 绑定项目事件
   */
  bindItemEvents() {
    // 复选框事件
    DOMUtils.$$('.btn-checkbox').forEach(btn => {
      DOMUtils.on(btn, 'click', (e) => {
        e.stopPropagation();
        const id = parseInt(DOMUtils.getAttr(btn, 'data-id'));
        this.eventManager.emit(AppConstants.EVENTS.ITEM_COMPLETED, {
          id: id,
          completed: !DOMUtils.hasClass(btn, 'checked')
        });
      });
    });

    // 折叠按钮事件
    DOMUtils.$$('.collapse-btn').forEach(btn => {
      DOMUtils.on(btn, 'click', (e) => {
        e.stopPropagation();
        const id = parseInt(DOMUtils.getAttr(btn, 'data-id'));
        this.eventManager.emit(AppConstants.EVENTS.ITEM_COLLAPSED, {
          id: id,
          collapsed: !DOMUtils.hasClass(btn, 'collapsed')
        });
      });
    });

    // 计时器按钮事件
    DOMUtils.$$('.btn-timer').forEach(btn => {
      DOMUtils.on(btn, 'click', (e) => {
        e.stopPropagation();
        const id = parseInt(DOMUtils.getAttr(btn, 'data-id'));
        const item = this.findItemById(id);
        if (item) {
          this.eventManager.emit(AppConstants.EVENTS.STUDY_SESSION_STARTED, {
            item: item
          });
        }
      });
    });

    // 节点点击事件 - 显示内容
    DOMUtils.$$('.tree-node-header, .tree-node-content').forEach(node => {
      DOMUtils.on(node, 'click', (e) => {
        // 避免按钮事件冒泡
        if (e.target.closest('.btn-checkbox, .collapse-btn, .btn-timer')) {
          return;
        }

        const itemElement = e.currentTarget.closest('[data-id]');
        if (itemElement) {
          const id = parseInt(DOMUtils.getAttr(itemElement, 'data-id'));
          const item = this.findItemById(id);
          if (item) {
            this.eventManager.emit(AppConstants.EVENTS.ITEM_SELECTED, { item });
            this.highlightSelectedNode(itemElement);
          }
        }
      });
    });
  }

  /**
   * 高亮选中的节点
   * @param {Element} element - 要高亮的元素
   */
  highlightSelectedNode(element) {
    // 移除之前的高亮
    DOMUtils.$$('.tree-node-header, .tree-node-content').forEach(node => {
      DOMUtils.removeClass(node, 'selected');
    });

    // 添加当前高亮
    DOMUtils.addClass(element, 'selected');
  }

  /**
   * 根据ID查找项目
   * @param {number} id - 项目ID
   * @param {Array} items - 搜索范围
   * @returns {Object|null} 找到的项目或null
   */
  findItemById(id, items = this.roadmapData) {
    for (let item of items) {
      if (item.id === id) return item;
      if (item.children) {
        const found = this.findItemById(id, item.children);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * 显示空状态
   */
  showEmptyState() {
    if (!this.elements.roadmapContent) return;

    const html = `
      <div class="empty-state">
        <div class="empty-icon">${AppConstants.ICONS.ROADMAP}</div>
        <h3>${AppConstants.DEFAULT_TEXT.EMPTY_STATE_TITLE}</h3>
        <p>${AppConstants.DEFAULT_TEXT.EMPTY_STATE_DESC}</p>
      </div>
    `;

    DOMUtils.setContent(this.elements.roadmapContent, html, true);
  }

  /**
   * 显示无搜索结果
   */
  showNoSearchResults() {
    if (!this.elements.roadmapContent) return;

    const html = `
      <div class="empty-state">
        <div class="empty-icon">${AppConstants.ICONS.SEARCH}</div>
        <h3>${AppConstants.DEFAULT_TEXT.NO_SEARCH_RESULTS}</h3>
        <p>尝试使用其他关键词搜索</p>
        <button class="btn-secondary" onclick="window.app.clearSearch()">
          清空搜索
        </button>
      </div>
    `;

    DOMUtils.setContent(this.elements.roadmapContent, html, true);
  }

  /**
   * 重置内容区域
   */
  resetContentArea() {
    if (this.elements.contentTitle) {
      DOMUtils.setContent(this.elements.contentTitle, `${AppConstants.ICONS.DETAIL} 课程详情`);
    }

    if (this.elements.contentBody) {
      const html = `
        <div class="welcome-content">
          <div class="welcome-icon">${AppConstants.ICONS.PROGRESS}</div>
          <h2>${AppConstants.DEFAULT_TEXT.WELCOME_TITLE}</h2>
          <p>${AppConstants.DEFAULT_TEXT.WELCOME_DESC}</p>
        </div>
      `;
      DOMUtils.setContent(this.elements.contentBody, html, true);
    }

    if (this.elements.markCompleteBtn) {
      this.elements.markCompleteBtn.disabled = true;
    }

    if (this.elements.startStudyBtn) {
      this.elements.startStudyBtn.disabled = true;
    }
  }

  /**
   * 根据搜索条件过滤项目
   * @param {Array} items - 项目数组
   * @returns {Array} 过滤后的项目数组
   */
  filterItemsBySearch(items) {
    if (!this.searchTerm) return items;

    return items.filter(item => {
      const matchesTitle = item.title.toLowerCase().includes(this.searchTerm.toLowerCase());
      const hasMatchingChildren = item.children &&
        this.filterItemsBySearch(item.children).length > 0;

      return matchesTitle || hasMatchingChildren;
    }).map(item => {
      if (item.children) {
        return {
          ...item,
          children: this.filterItemsBySearch(item.children)
        };
      }
      return item;
    });
  }

  /**
   * 过滤侧边栏显示的项目（只显示前2级）
   * @param {Array} items - 项目数组
   * @returns {Array} 过滤后的项目数组
   */
  filterSidebarItems(items) {
    return items.map(item => {
      if (item.level <= AppConstants.CONFIG.SIDEBAR_MAX_LEVEL) {
        // 对于1-2级项目，只保留同样是1-2级的子项
        const sidebarChildren = item.children ?
          item.children.filter(child => child.level <= AppConstants.CONFIG.SIDEBAR_MAX_LEVEL) : [];

        return {
          ...item,
          children: this.filterSidebarItems(sidebarChildren)
        };
      }
      return null;
    }).filter(Boolean);
  }

  /**
   * 显示项目内容
   * @param {Object} item - 项目对象
   */
  showItemContent(item) {
    this.selectedItem = item;

    if (this.elements.contentTitle) {
      DOMUtils.setContent(this.elements.contentTitle, `${AppConstants.ICONS.DETAIL} ${item.title}`);
    }

    // 启用操作按钮
    if (this.elements.markCompleteBtn) {
      this.elements.markCompleteBtn.disabled = false;
      this.elements.markCompleteBtn.textContent = item.completed ? '取消完成' : '标记完成';
      this.elements.markCompleteBtn.className = item.completed ? 'btn-secondary' : 'btn-primary';
    }

    if (this.elements.startStudyBtn) {
      this.elements.startStudyBtn.disabled = false;
    }

    // 生成内容
    const contentHtml = this.generateItemContentHtml(item);
    if (this.elements.contentBody) {
      DOMUtils.setContent(this.elements.contentBody, contentHtml, true);
    }
  }

  /**
   * 生成项目内容HTML
   * @param {Object} item - 项目对象
   * @returns {string} HTML字符串
   */
  generateItemContentHtml(item) {
    const progressInfo = item.children ? this.getChildrenProgress(item) : '';
    const timeSpent = TimeUtils.formatTime(item.timeSpent || 0);
    const deepChildren = this.getDeepChildren(item);

    return `
      <div class="item-detail">
        <div class="detail-header">
          <div class="item-type-badge ${item.type}">${this.getTypeLabel(item.type)}</div>
          <div class="item-status ${item.completed ? 'completed' : 'pending'}">
            ${item.completed ? '✅ 已完成' : '⏳ 进行中'}
          </div>
        </div>

        <div class="detail-content">
          <h3>${item.title}</h3>

          <div class="detail-stats">
            <div class="stat-item">
              <span class="stat-label">学习时间:</span>
              <span class="stat-value">${timeSpent}</span>
            </div>
            ${progressInfo ? `
              <div class="stat-item">
                <span class="stat-label">子项进度:</span>
                <span class="stat-value">${progressInfo}</span>
              </div>
            ` : ''}
            <div class="stat-item">
              <span class="stat-label">层级:</span>
              <span class="stat-value">第 ${item.level} 级</span>
            </div>
            ${deepChildren.length > 0 ? `
              <div class="stat-item">
                <span class="stat-label">详细内容:</span>
                <span class="stat-value">${deepChildren.length} 项</span>
              </div>
            ` : ''}
          </div>

          ${item.children && item.children.filter(child => child.level <= AppConstants.CONFIG.SIDEBAR_MAX_LEVEL).length > 0 ? `
            <div class="children-overview">
              <h4>${AppConstants.ICONS.ROADMAP} 直接子项目</h4>
              <div class="children-list">
                ${item.children.filter(child => child.level <= AppConstants.CONFIG.SIDEBAR_MAX_LEVEL).map(child => `
                  <div class="child-item ${child.completed ? 'completed' : ''}" data-id="${child.id}">
                    <span class="child-status">${child.completed ? '✅' : '⏳'}</span>
                    <span class="child-title">${child.title}</span>
                    <span class="child-time">${TimeUtils.formatTime(child.timeSpent || 0)}</span>
                  </div>
                `).join('')}
              </div>
            </div>
          ` : ''}

          ${item.notes ? `
            <div class="item-notes-display">
              <h4>${AppConstants.ICONS.FILE} 学习笔记</h4>
              <div class="notes-content">${item.notes}</div>
            </div>
          ` : ''}
        </div>
      </div>
    `;
  }

  /**
   * 获取项目的深层级子项（3级及以上）
   * @param {Object} item - 项目对象
   * @returns {Array} 深层级子项数组
   */
  getDeepChildren(item) {
    const deepChildren = [];

    const collectDeepChildren = (node) => {
      if (node.children) {
        node.children.forEach(child => {
          if (child.level > AppConstants.CONFIG.SIDEBAR_MAX_LEVEL) {
            deepChildren.push(child);
          }
          collectDeepChildren(child);
        });
      }
    };

    collectDeepChildren(item);
    return deepChildren;
  }

  /**
   * 获取类型标签
   * @param {string} type - 项目类型
   * @returns {string} 类型标签
   */
  getTypeLabel(type) {
    return AppConstants.TYPE_LABELS[type] || AppConstants.TYPE_LABELS.item;
  }

  /**
   * 销毁模块
   */
  destroy() {
    // 移除事件监听器
    this.eventManager.removeAllListeners(AppConstants.EVENTS.ROADMAP_UPDATED);
    this.eventManager.removeAllListeners(AppConstants.EVENTS.DATA_LOADED);
    this.eventManager.removeAllListeners(AppConstants.EVENTS.DATA_CLEARED);
    this.eventManager.removeAllListeners(AppConstants.EVENTS.SEARCH_PERFORMED);
    this.eventManager.removeAllListeners(AppConstants.EVENTS.SEARCH_CLEARED);
    this.eventManager.removeAllListeners(AppConstants.EVENTS.ITEM_SELECTED);
    this.eventManager.removeAllListeners(AppConstants.EVENTS.STATS_UPDATED);

    // 清理数据
    this.roadmapData = [];
    this.selectedItem = null;
    this.searchTerm = '';
  }
}

// 导出模块
window.RenderEngine = RenderEngine;
