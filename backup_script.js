// ===== 备份文件：原始script.js =====
// 备份时间：2024-12-19
// 备份原因：优化前的代码状态保存

// ===== 全局变量和状态管理 =====
class LearningRoadmapApp {
    constructor() {
      // 应用状态
      this.roadmapData = [];
      this.learningProgress = {};
      this.studyTime = {};
      this.currentTimer = null;
      this.timerInterval = null;
      this.currentStudyItem = null;
      this.sessionStartTime = null;
      
      // DOM 元素引用
      this.elements = {
        // 上传相关
        fileInput: document.getElementById('fileInput'),
        uploadArea: document.getElementById('uploadArea'),
        parseBtn: document.getElementById('parseBtn'),
        clearBtn: document.getElementById('clearBtn'),
        
        // 统计显示
        totalItems: document.getElementById('totalItems'),
        completedItems: document.getElementById('completedItems'),
        totalTime: document.getElementById('totalTime'),
        progressPercent: document.getElementById('progressPercent'),
        progressFill: document.getElementById('progressFill'),
        
        // 控制按钮
        darkModeToggle: document.getElementById('darkModeToggle'),
        collapseAllBtn: document.getElementById('collapseAllBtn'),
        expandAllBtn: document.getElementById('expandAllBtn'),
        resetProgressBtn: document.getElementById('resetProgressBtn'),
        
        // 内容展示
        roadmapContent: document.getElementById('roadmapContent'),
        
        // 时间追踪
        timeTracker: document.getElementById('timeTracker'),
        closeTracker: document.getElementById('closeTracker'),
        currentItem: document.getElementById('currentItem'),
        timerDisplay: document.getElementById('timerDisplay'),
        startTimer: document.getElementById('startTimer'),
        pauseTimer: document.getElementById('pauseTimer'),
        stopTimer: document.getElementById('stopTimer'),
        sessionNotes: document.getElementById('sessionNotes'),
        saveNotes: document.getElementById('saveNotes'),
        
        // 模态框
        modalOverlay: document.getElementById('modalOverlay')
      };
      
      this.init();
    }
    
    // ===== 初始化方法 =====
    init() {
      this.loadStoredData();
      this.bindEvents();
      this.updateStats();
      this.initTheme();
    }
    
    // ===== 事件绑定 =====
    bindEvents() {
      // 文件上传相关
      this.elements.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
      this.elements.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
      this.elements.uploadArea.addEventListener('drop', (e) => this.handleFileDrop(e));
      this.elements.uploadArea.addEventListener('click', () => this.elements.fileInput.click());
      
      // 按钮事件
      this.elements.parseBtn.addEventListener('click', () => this.parseMarkdown());
      this.elements.clearBtn.addEventListener('click', () => this.clearContent());
      this.elements.darkModeToggle.addEventListener('click', () => this.toggleDarkMode());
      this.elements.collapseAllBtn.addEventListener('click', () => this.collapseAll());
      this.elements.expandAllBtn.addEventListener('click', () => this.expandAll());
      this.elements.resetProgressBtn.addEventListener('click', () => this.resetProgress());
      
      // 时间追踪相关
      this.elements.closeTracker.addEventListener('click', () => this.closeTimeTracker());
      this.elements.startTimer.addEventListener('click', () => this.startTimer());
      this.elements.pauseTimer.addEventListener('click', () => this.pauseTimer());
      this.elements.stopTimer.addEventListener('click', () => this.stopTimer());
      this.elements.saveNotes.addEventListener('click', () => this.saveSessionNotes());
      
      // 模态框关闭
      this.elements.modalOverlay.addEventListener('click', () => this.closeTimeTracker());
    }
    
    // ===== 文件处理方法 =====
    handleFileSelect(event) {
      const file = event.target.files[0];
      if (file) {
        this.readFile(file);
      }
    }
    
    handleDragOver(event) {
      event.preventDefault();
      this.elements.uploadArea.classList.add('dragover');
    }
    
    handleFileDrop(event) {
      event.preventDefault();
      this.elements.uploadArea.classList.remove('dragover');
      
      const files = event.dataTransfer.files;
      if (files.length > 0) {
        this.readFile(files[0]);
      }
    }
    
    readFile(file) {
      if (!file.name.match(/\.(md|txt)$/i)) {
        alert('请选择 Markdown (.md) 或文本 (.txt) 文件');
        return;
      }
      
      const reader = new FileReader();
      reader.onload = (e) => {
        this.rawMarkdown = e.target.result;
        this.elements.parseBtn.disabled = false;
        this.updateUploadDisplay(file.name);
      };
      reader.readAsText(file, 'UTF-8');
    }
    
    updateUploadDisplay(filename) {
      const uploadContent = this.elements.uploadArea.querySelector('.upload-content');
      uploadContent.innerHTML = `
        <div class="upload-icon">📄</div>
        <p class="upload-text">已选择文件: ${filename}</p>
        <button class="btn-secondary" onclick="document.getElementById('fileInput').click()">
          重新选择
        </button>
      `;
    }
    
    // ===== Markdown 解析方法 =====
    parseMarkdown() {
      if (!this.rawMarkdown) return;
      
      const lines = this.rawMarkdown.split('\n');
      this.roadmapData = [];
      
      let currentSection = null;
      let currentSubsection = null;
      let itemId = 0;
      
      lines.forEach(line => {
        const trimmedLine = line.trim();
        if (!trimmedLine) return;
        
        // 检测标题级别
        const headerMatch = trimmedLine.match(/^(#{1,6})\s+(.+)$/);
        if (headerMatch) {
          const level = headerMatch[1].length;
          const title = headerMatch[2];
          
          if (level === 1) {
            // 一级标题 - 主要章节
            currentSection = {
              id: ++itemId,
              type: 'section',
              level: 1,
              title: title,
              completed: false,
              children: [],
              collapsed: false
            };
            this.roadmapData.push(currentSection);
            currentSubsection = null;
          } else if (level === 2 && currentSection) {
            // 二级标题 - 子章节
            currentSubsection = {
              id: ++itemId,
              type: 'subsection',
              level: 2,
              title: title,
              completed: false,
              children: [],
              collapsed: false
            };
            currentSection.children.push(currentSubsection);
          } else if (level >= 3 && currentSubsection) {
            // 三级及以上标题 - 学习项目
            const item = {
              id: ++itemId,
              type: 'item',
              level: level,
              title: title,
              completed: false,
              timeSpent: 0,
              notes: ''
            };
            currentSubsection.children.push(item);
          } else if (level >= 3 && currentSection && !currentSubsection) {
            // 直接在主章节下的学习项目
            const item = {
              id: ++itemId,
              type: 'item',
              level: level,
              title: title,
              completed: false,
              timeSpent: 0,
              notes: ''
            };
            currentSection.children.push(item);
          }
        } else if (trimmedLine.startsWith('-') || trimmedLine.startsWith('*')) {
          // 列表项目
          const title = trimmedLine.replace(/^[-*]\s*/, '');
          const item = {
            id: ++itemId,
            type: 'item',
            level: 0,
            title: title,
            completed: false,
            timeSpent: 0,
            notes: ''
          };
          
          if (currentSubsection) {
            currentSubsection.children.push(item);
          } else if (currentSection) {
            currentSection.children.push(item);
          } else {
            this.roadmapData.push(item);
          }
        }
      });
      
      this.renderRoadmap();
      this.updateStats();
      this.saveData();
    }
    
    // ===== 继续备份其余代码... =====
    // 注意：由于文件长度限制，这里只备份了前面的核心部分
    // 完整备份请参考原始script.js文件
}

// 备份说明：
// 1. 这是优化前的原始代码状态
// 2. 主要备份了parseMarkdown方法的原始实现
// 3. 如需完整备份，请查看原始script.js文件
// 4. 备份目的：确保优化过程中可以回滚
