/**
 * 统计面板组件
 * 负责统计数据计算和显示
 */
class StatsPanel {
  constructor(eventManager) {
    this.eventManager = eventManager;
    
    // DOM元素引用
    this.elements = {
      totalItems: DOMUtils.$(AppConstants.SELECTORS.TOTAL_ITEMS),
      completedItems: DOMUtils.$(AppConstants.SELECTORS.COMPLETED_ITEMS),
      totalTime: DOMUtils.$(AppConstants.SELECTORS.TOTAL_TIME),
      progressPercent: DOMUtils.$(AppConstants.SELECTORS.PROGRESS_PERCENT),
      progressFill: DOMUtils.$(AppConstants.SELECTORS.PROGRESS_FILL),
      resetProgressBtn: DOMUtils.$(AppConstants.SELECTORS.RESET_PROGRESS_BTN)
    };

    // 当前统计数据
    this.currentStats = {
      totalItems: 0,
      completedItems: 0,
      totalTime: 0,
      progressPercent: 0,
      formattedTime: '0s'
    };

    this.init();
  }

  /**
   * 初始化统计面板组件
   */
  init() {
    this.bindEvents();
    this.updateDisplay();
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 监听统计更新事件
    this.eventManager.on(AppConstants.EVENTS.STATS_UPDATED, (data) => {
      this.updateStats(data.stats);
    });

    // 监听进度更新事件
    this.eventManager.on(AppConstants.EVENTS.PROGRESS_UPDATED, (data) => {
      this.updateProgress(data.progress);
    });

    // 监听数据清空事件
    this.eventManager.on(AppConstants.EVENTS.DATA_CLEARED, () => {
      this.resetStats();
    });

    // 绑定重置进度按钮事件
    if (this.elements.resetProgressBtn) {
      DOMUtils.on(this.elements.resetProgressBtn, 'click', () => {
        this.handleResetProgress();
      });
    }
  }

  /**
   * 更新统计数据
   * @param {Object} stats - 统计数据对象
   */
  updateStats(stats) {
    if (!stats || typeof stats !== 'object') {
      console.warn('无效的统计数据:', stats);
      return;
    }

    // 更新内部数据
    this.currentStats = {
      totalItems: stats.totalItems || 0,
      completedItems: stats.completedItems || 0,
      totalTime: stats.totalTime || 0,
      progressPercent: stats.progressPercent || 0,
      formattedTime: stats.formattedTime || TimeUtils.formatTime(stats.totalTime || 0, false)
    };

    // 更新显示
    this.updateDisplay();

    // 添加动画效果
    this.animateUpdate();
  }

  /**
   * 更新进度百分比
   * @param {number} progress - 进度百分比
   */
  updateProgress(progress) {
    if (typeof progress === 'number' && progress >= 0 && progress <= 100) {
      this.currentStats.progressPercent = progress;
      this.updateProgressDisplay();
    }
  }

  /**
   * 更新显示内容
   */
  updateDisplay() {
    // 更新总项目数
    if (this.elements.totalItems) {
      DOMUtils.setContent(this.elements.totalItems, this.currentStats.totalItems.toString());
    }

    // 更新已完成项目数
    if (this.elements.completedItems) {
      DOMUtils.setContent(this.elements.completedItems, this.currentStats.completedItems.toString());
    }

    // 更新总学习时间
    if (this.elements.totalTime) {
      DOMUtils.setContent(this.elements.totalTime, this.currentStats.formattedTime);
    }

    // 更新进度百分比
    this.updateProgressDisplay();
  }

  /**
   * 更新进度显示
   */
  updateProgressDisplay() {
    // 更新进度百分比文本
    if (this.elements.progressPercent) {
      DOMUtils.setContent(this.elements.progressPercent, `${this.currentStats.progressPercent}%`);
    }

    // 更新进度条填充
    if (this.elements.progressFill) {
      DOMUtils.setStyle(this.elements.progressFill, 'width', `${this.currentStats.progressPercent}%`);
      
      // 根据进度添加不同的颜色类
      this.updateProgressColor();
    }
  }

  /**
   * 根据进度更新进度条颜色
   */
  updateProgressColor() {
    if (!this.elements.progressFill) return;

    const progress = this.currentStats.progressPercent;
    
    // 移除之前的颜色类
    DOMUtils.removeClass(this.elements.progressFill, ['progress-low', 'progress-medium', 'progress-high', 'progress-complete']);

    // 根据进度添加相应的颜色类
    if (progress === 100) {
      DOMUtils.addClass(this.elements.progressFill, 'progress-complete');
    } else if (progress >= 75) {
      DOMUtils.addClass(this.elements.progressFill, 'progress-high');
    } else if (progress >= 50) {
      DOMUtils.addClass(this.elements.progressFill, 'progress-medium');
    } else if (progress > 0) {
      DOMUtils.addClass(this.elements.progressFill, 'progress-low');
    }
  }

  /**
   * 添加更新动画效果
   */
  animateUpdate() {
    // 为统计卡片添加更新动画
    const statCards = [
      this.elements.totalItems?.closest('.stat-card'),
      this.elements.completedItems?.closest('.stat-card'),
      this.elements.totalTime?.closest('.stat-card'),
      this.elements.progressPercent?.closest('.stat-card')
    ].filter(Boolean);

    statCards.forEach(card => {
      if (card) {
        DOMUtils.addClass(card, 'stat-updated');
        
        // 移除动画类
        setTimeout(() => {
          DOMUtils.removeClass(card, 'stat-updated');
        }, 600);
      }
    });
  }

  /**
   * 重置统计数据
   */
  resetStats() {
    this.currentStats = {
      totalItems: 0,
      completedItems: 0,
      totalTime: 0,
      progressPercent: 0,
      formattedTime: '0s'
    };

    this.updateDisplay();
  }

  /**
   * 处理重置进度按钮点击
   */
  handleResetProgress() {
    if (confirm(AppConstants.CONFIRM_MESSAGES.RESET_PROGRESS)) {
      // 触发重置进度事件，由DataManager处理
      this.eventManager.emit('progress:reset');
      
      // 显示成功消息
      this.showSuccessMessage(AppConstants.SUCCESS_MESSAGES.PROGRESS_RESET);
    }
  }

  /**
   * 显示成功消息
   * @param {string} message - 消息内容
   */
  showSuccessMessage(message) {
    // 创建临时提示元素
    const notification = DOMUtils.createElement('div', {
      className: 'success-notification',
      textContent: message
    });

    // 添加到页面
    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => {
      DOMUtils.addClass(notification, 'show');
    }, 10);

    // 自动移除
    setTimeout(() => {
      DOMUtils.removeClass(notification, 'show');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 2000);
  }

  /**
   * 获取当前统计数据
   * @returns {Object} 当前统计数据
   */
  getCurrentStats() {
    return { ...this.currentStats };
  }

  /**
   * 格式化统计数据用于显示
   * @param {Object} stats - 原始统计数据
   * @returns {Object} 格式化后的统计数据
   */
  formatStatsForDisplay(stats) {
    return {
      totalItems: stats.totalItems || 0,
      completedItems: stats.completedItems || 0,
      totalTime: TimeUtils.formatTime(stats.totalTime || 0, false),
      progressPercent: Math.round(stats.progressPercent || 0),
      completionRate: stats.totalItems > 0 ? 
        Math.round((stats.completedItems / stats.totalItems) * 100) : 0
    };
  }

  /**
   * 获取进度状态描述
   * @returns {string} 进度状态描述
   */
  getProgressStatus() {
    const progress = this.currentStats.progressPercent;
    
    if (progress === 100) {
      return '🎉 恭喜完成！';
    } else if (progress >= 75) {
      return '🚀 即将完成';
    } else if (progress >= 50) {
      return '💪 进展良好';
    } else if (progress >= 25) {
      return '📈 稳步前进';
    } else if (progress > 0) {
      return '🌱 刚刚开始';
    } else {
      return '📋 等待开始';
    }
  }

  /**
   * 导出统计数据
   * @returns {Object} 导出的统计数据
   */
  exportStats() {
    return {
      ...this.currentStats,
      exportTime: Date.now(),
      progressStatus: this.getProgressStatus(),
      formattedStats: this.formatStatsForDisplay(this.currentStats)
    };
  }

  /**
   * 检查是否有学习活动
   * @returns {boolean} 是否有学习活动
   */
  hasLearningActivity() {
    return this.currentStats.totalItems > 0 || this.currentStats.totalTime > 0;
  }

  /**
   * 获取学习效率统计
   * @returns {Object} 学习效率统计
   */
  getEfficiencyStats() {
    const { totalItems, completedItems, totalTime } = this.currentStats;
    
    return {
      averageTimePerItem: totalItems > 0 ? Math.round(totalTime / totalItems) : 0,
      averageTimePerCompletedItem: completedItems > 0 ? Math.round(totalTime / completedItems) : 0,
      completionRate: totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0,
      formattedAverageTime: totalItems > 0 ? 
        TimeUtils.formatTime(Math.round(totalTime / totalItems)) : '0s'
    };
  }

  /**
   * 销毁组件
   */
  destroy() {
    // 移除事件监听器
    this.eventManager.removeAllListeners(AppConstants.EVENTS.STATS_UPDATED);
    this.eventManager.removeAllListeners(AppConstants.EVENTS.PROGRESS_UPDATED);
    this.eventManager.removeAllListeners(AppConstants.EVENTS.DATA_CLEARED);
    
    // 清理数据
    this.currentStats = null;
    this.elements = {};
  }
}

// 导出组件
window.StatsPanel = StatsPanel;
