/**
 * 文件处理模块
 * 负责文件上传、拖拽、读取等功能
 */
class FileHandler {
  constructor(eventManager) {
    this.eventManager = eventManager;
    this.rawMarkdown = '';
    
    // DOM元素引用
    this.elements = {
      fileInput: DOMUtils.$(AppConstants.SELECTORS.FILE_INPUT),
      uploadArea: DOMUtils.$(AppConstants.SELECTORS.UPLOAD_AREA),
      parseBtn: DOMUtils.$(AppConstants.SELECTORS.PARSE_BTN),
      clearBtn: DOMUtils.$(AppConstants.SELECTORS.CLEAR_BTN)
    };

    this.init();
  }

  /**
   * 初始化文件处理模块
   */
  init() {
    this.bindEvents();
    this.resetUploadArea();
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 文件选择事件
    if (this.elements.fileInput) {
      DOMUtils.on(this.elements.fileInput, 'change', (e) => this.handleFileSelect(e));
    }

    // 拖拽事件
    if (this.elements.uploadArea) {
      DOMUtils.on(this.elements.uploadArea, 'dragover', (e) => this.handleDragOver(e));
      DOMUtils.on(this.elements.uploadArea, 'drop', (e) => this.handleFileDrop(e));
      DOMUtils.on(this.elements.uploadArea, 'dragleave', (e) => this.handleDragLeave(e));
    }

    // 解析按钮事件
    if (this.elements.parseBtn) {
      DOMUtils.on(this.elements.parseBtn, 'click', () => this.parseMarkdown());
    }

    // 清空按钮事件
    if (this.elements.clearBtn) {
      DOMUtils.on(this.elements.clearBtn, 'click', () => this.clearContent());
    }

    // 监听应用清空事件
    this.eventManager.on(AppConstants.EVENTS.DATA_CLEARED, () => {
      this.resetUploadArea();
      this.rawMarkdown = '';
      if (this.elements.parseBtn) {
        this.elements.parseBtn.disabled = true;
      }
    });
  }

  /**
   * 处理文件选择事件
   * @param {Event} event - 文件选择事件
   */
  handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
      this.readFile(file);
    }
  }

  /**
   * 处理拖拽悬停事件
   * @param {Event} event - 拖拽事件
   */
  handleDragOver(event) {
    event.preventDefault();
    DOMUtils.addClass(this.elements.uploadArea, AppConstants.CSS_CLASSES.DRAGOVER);
  }

  /**
   * 处理拖拽离开事件
   * @param {Event} event - 拖拽事件
   */
  handleDragLeave(event) {
    event.preventDefault();
    DOMUtils.removeClass(this.elements.uploadArea, AppConstants.CSS_CLASSES.DRAGOVER);
  }

  /**
   * 处理文件拖拽放置事件
   * @param {Event} event - 拖拽事件
   */
  handleFileDrop(event) {
    event.preventDefault();
    DOMUtils.removeClass(this.elements.uploadArea, AppConstants.CSS_CLASSES.DRAGOVER);

    const files = event.dataTransfer.files;
    if (files.length > 0) {
      this.readFile(files[0]);
    }
  }

  /**
   * 读取文件内容
   * @param {File} file - 文件对象
   */
  readFile(file) {
    // 验证文件类型
    if (!this.validateFileType(file)) {
      this.eventManager.emit(AppConstants.EVENTS.FILE_UPLOAD_ERROR, {
        message: AppConstants.ERROR_MESSAGES.FILE_TYPE_ERROR
      });
      alert(AppConstants.ERROR_MESSAGES.FILE_TYPE_ERROR);
      return;
    }

    // 验证文件大小
    if (!this.validateFileSize(file)) {
      this.eventManager.emit(AppConstants.EVENTS.FILE_UPLOAD_ERROR, {
        message: AppConstants.ERROR_MESSAGES.FILE_SIZE_ERROR
      });
      alert(AppConstants.ERROR_MESSAGES.FILE_SIZE_ERROR);
      return;
    }

    const reader = new FileReader();
    
    reader.onload = (e) => {
      this.rawMarkdown = e.target.result;
      
      // 启用解析按钮
      if (this.elements.parseBtn) {
        this.elements.parseBtn.disabled = false;
      }
      
      // 更新上传显示
      this.updateUploadDisplay(file.name);
      
      // 触发文件选择事件
      this.eventManager.emit(AppConstants.EVENTS.FILE_SELECTED, {
        filename: file.name,
        content: this.rawMarkdown,
        size: file.size
      });
    };

    reader.onerror = () => {
      this.eventManager.emit(AppConstants.EVENTS.FILE_UPLOAD_ERROR, {
        message: '文件读取失败'
      });
      alert('文件读取失败，请重试');
    };

    reader.readAsText(file, 'UTF-8');
  }

  /**
   * 验证文件类型
   * @param {File} file - 文件对象
   * @returns {boolean} 是否为有效类型
   */
  validateFileType(file) {
    const validExtensions = AppConstants.CONFIG.ACCEPTED_FILE_TYPES;
    const fileName = file.name.toLowerCase();
    
    return validExtensions.some(ext => fileName.endsWith(ext));
  }

  /**
   * 验证文件大小
   * @param {File} file - 文件对象
   * @returns {boolean} 是否在大小限制内
   */
  validateFileSize(file) {
    return file.size <= AppConstants.CONFIG.MAX_FILE_SIZE;
  }

  /**
   * 更新上传区域显示
   * @param {string} filename - 文件名
   */
  updateUploadDisplay(filename) {
    if (!this.elements.uploadArea) return;

    const uploadContent = DOMUtils.$('.upload-content', this.elements.uploadArea);
    if (!uploadContent) return;

    uploadContent.innerHTML = `
      <div class="upload-icon">${AppConstants.ICONS.FILE}</div>
      <p class="upload-text">已选择文件: ${filename}</p>
      <input type="file" id="fileInput" accept="${AppConstants.CONFIG.ACCEPTED_FILE_TYPES.join(',')}" hidden>
      <button class="btn-secondary" id="reSelectBtn">重新选择</button>
    `;

    // 重新绑定新创建的元素事件
    this.rebindUploadEvents();
  }

  /**
   * 重新绑定上传相关事件
   */
  rebindUploadEvents() {
    const reSelectBtn = DOMUtils.$('#reSelectBtn');
    const newFileInput = DOMUtils.$('#fileInput');

    if (reSelectBtn && newFileInput) {
      DOMUtils.on(reSelectBtn, 'click', () => {
        newFileInput.click();
      });

      DOMUtils.on(newFileInput, 'change', (e) => this.handleFileSelect(e));
      
      // 更新元素引用
      this.elements.fileInput = newFileInput;
    }
  }

  /**
   * 解析Markdown文件
   */
  parseMarkdown() {
    if (!this.rawMarkdown) {
      alert('请先选择文件');
      return;
    }

    try {
      // 触发文件解析事件，由MarkdownParser模块处理
      this.eventManager.emit(AppConstants.EVENTS.FILE_PARSED, {
        content: this.rawMarkdown
      });
    } catch (error) {
      console.error('解析文件失败:', error);
      this.eventManager.emit(AppConstants.EVENTS.FILE_UPLOAD_ERROR, {
        message: AppConstants.ERROR_MESSAGES.PARSE_ERROR
      });
      alert(AppConstants.ERROR_MESSAGES.PARSE_ERROR);
    }
  }

  /**
   * 清空内容
   */
  clearContent() {
    if (confirm(AppConstants.CONFIRM_MESSAGES.CLEAR_CONTENT)) {
      this.rawMarkdown = '';
      
      if (this.elements.parseBtn) {
        this.elements.parseBtn.disabled = true;
      }
      
      this.resetUploadArea();
      
      // 触发数据清空事件
      this.eventManager.emit(AppConstants.EVENTS.DATA_CLEARED);
    }
  }

  /**
   * 重置上传区域
   */
  resetUploadArea() {
    if (!this.elements.uploadArea) return;

    const uploadContent = DOMUtils.$('.upload-content', this.elements.uploadArea);
    if (!uploadContent) return;

    uploadContent.innerHTML = `
      <div class="upload-icon">${AppConstants.ICONS.UPLOAD}</div>
      <p class="upload-text">拖拽文件到这里或点击选择</p>
      <input type="file" id="fileInput" accept="${AppConstants.CONFIG.ACCEPTED_FILE_TYPES.join(',')}" hidden>
      <button class="btn-primary" onclick="document.getElementById('fileInput').click()">
        选择文件
      </button>
    `;

    // 重新绑定事件
    this.rebindUploadEvents();
  }

  /**
   * 获取当前文件内容
   * @returns {string} 文件内容
   */
  getFileContent() {
    return this.rawMarkdown;
  }

  /**
   * 检查是否有文件内容
   * @returns {boolean} 是否有内容
   */
  hasContent() {
    return !!this.rawMarkdown;
  }

  /**
   * 销毁模块
   */
  destroy() {
    // 移除事件监听器
    this.eventManager.removeAllListeners(AppConstants.EVENTS.DATA_CLEARED);
    
    // 清理数据
    this.rawMarkdown = '';
    this.elements = {};
  }
}

// 导出模块
window.FileHandler = FileHandler;
